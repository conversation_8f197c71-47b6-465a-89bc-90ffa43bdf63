#!/usr/bin/env python3
"""
Test the Simple Cryptocurrency Dashboard API
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def test_endpoint(endpoint, description):
    """Test an API endpoint"""
    print(f"\n🔍 Testing: {description}")
    print(f"📡 Endpoint: {endpoint}")
    
    try:
        response = requests.get(f"{API_BASE}{endpoint}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: {response.status_code}")
            print(f"📊 Response: {json.dumps(data, indent=2)}")
            return data
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Exception: {e}")
        return None

def main():
    """Run API tests"""
    print("🧪 Testing Simple Cryptocurrency Dashboard API")
    print("=" * 60)
    
    # Test 1: Health check
    test_endpoint("/health", "Health Check")
    
    # Test 2: Root endpoint
    test_endpoint("/", "Root Endpoint")
    
    # Test 3: Get exchanges
    exchanges = test_endpoint("/exchanges", "Available Exchanges")
    
    if exchanges:
        # Test 4: Get symbols from first exchange
        exchange_name = exchanges[0]["name"]
        test_endpoint(f"/exchanges/{exchange_name}/symbols?limit=5", f"Symbols from {exchange_name}")
        
        # Test 5: Get BTC/USDT price
        test_endpoint(f"/exchanges/{exchange_name}/symbols/BTC/USDT/price", f"BTC/USDT Price from {exchange_name}")
        
        # Test 6: Get historical data
        test_endpoint(f"/exchanges/{exchange_name}/symbols/BTC/USDT/ohlcv?timeframe=1d&limit=3", f"BTC/USDT Historical Data from {exchange_name}")
    
    print("\n" + "=" * 60)
    print("🎉 API Testing Complete!")
    print("💡 The Simple API is working and ready to use!")
    print("\n📚 Available Endpoints:")
    print("  • GET /health - Health check")
    print("  • GET /exchanges - List exchanges")
    print("  • GET /exchanges/{exchange}/symbols - Get symbols")
    print("  • GET /exchanges/{exchange}/symbols/{symbol}/price - Get current price")
    print("  • GET /exchanges/{exchange}/symbols/{symbol}/ohlcv - Get historical data")

if __name__ == "__main__":
    main()
