"""
Technical indicators calculation module
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from models import TechnicalIndicators, FibonacciLevels

class TechnicalAnalysis:
    """Class for calculating technical indicators"""
    
    def calculate_rsi(self, series: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI with improved error handling"""
        if len(series) < period:
            return pd.Series([50] * len(series), index=series.index)
            
        delta = series.diff(1)
        gain = delta.where(delta > 0, 0.0)
        loss = -delta.where(delta < 0, 0.0)
        
        avg_gain = gain.rolling(window=period, min_periods=period).mean()
        avg_loss = loss.rolling(window=period, min_periods=period).mean()
        
        # Avoid division by zero
        avg_loss = avg_loss.replace(0, 1e-10)
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi.fillna(50)
    
    def calculate_macd(self, series: pd.Series, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD with validation"""
        if len(series) < slow_period:
            return (
                pd.Series([0] * len(series), index=series.index),
                pd.Series([0] * len(series), index=series.index),
                pd.Series([0] * len(series), index=series.index)
            )
        
        ema_fast = series.ewm(span=fast_period, adjust=False).mean()
        ema_slow = series.ewm(span=slow_period, adjust=False).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    def calculate_bollinger_bands(self, series: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands"""
        if len(series) < period:
            return (
                pd.Series([series.mean()] * len(series), index=series.index),
                pd.Series([series.mean()] * len(series), index=series.index),
                pd.Series([series.mean()] * len(series), index=series.index)
            )
        
        rolling_mean = series.rolling(window=period).mean()
        rolling_std = series.rolling(window=period).std()
        
        upper_band = rolling_mean + (rolling_std * std_dev)
        lower_band = rolling_mean - (rolling_std * std_dev)
        
        return upper_band, rolling_mean, lower_band
    
    def calculate_fibonacci_retracements(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate Fibonacci retracements with validation"""
        if df.empty or len(df) < 2:
            return {}
        
        price_min = df['low'].min()
        price_max = df['high'].max()
        
        if price_max == price_min:
            return {'50%': price_max}
        
        diff = price_max - price_min
        
        levels = {
            '0%': price_max,
            '23.6%': price_max - (0.236 * diff),
            '38.2%': price_max - (0.382 * diff),
            '50%': price_max - (0.500 * diff),
            '61.8%': price_max - (0.618 * diff),
            '78.6%': price_max - (0.786 * diff),
            '100%': price_min
        }
        return levels
    
    def determine_trend(self, rsi: float) -> str:
        """Determine trend based on RSI"""
        if pd.isna(rsi):
            return "Hold"
        if rsi < 30:
            return "Buy"
        elif rsi > 70:
            return "Sell"
        else:
            return "Hold"
    
    def detect_price_anomaly(self, df_close: pd.Series, threshold_std_dev: float = 3) -> Optional[str]:
        """Detect price anomalies with improved validation"""
        if len(df_close) < 5:  # Need more data points
            return None
        
        daily_returns = df_close.pct_change().dropna()
        if daily_returns.empty or len(daily_returns) < 2:
            return None

        mean_return = daily_returns.mean()
        std_return = daily_returns.std()

        if std_return == 0 or pd.isna(std_return):
            return None

        last_return = daily_returns.iloc[-1]
        if pd.isna(last_return):
            return None
            
        z_score = (last_return - mean_return) / std_return

        if abs(z_score) > threshold_std_dev:
            return f"Significant price movement detected! Z-score: {z_score:.2f}"
        return None
    
    def get_technical_indicators(self, df: pd.DataFrame, symbol: str) -> TechnicalIndicators:
        """Calculate all technical indicators for a symbol"""
        if df.empty:
            return TechnicalIndicators(symbol=symbol)
        
        close_prices = df['close']
        
        # Calculate indicators
        rsi_series = self.calculate_rsi(close_prices)
        macd_line, macd_signal, macd_histogram = self.calculate_macd(close_prices)
        bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(close_prices)
        
        # Get latest values
        current_rsi = rsi_series.iloc[-1] if not rsi_series.empty else None
        current_macd_line = macd_line.iloc[-1] if not macd_line.empty else None
        current_macd_signal = macd_signal.iloc[-1] if not macd_signal.empty else None
        current_macd_histogram = macd_histogram.iloc[-1] if not macd_histogram.empty else None
        current_bb_upper = bb_upper.iloc[-1] if not bb_upper.empty else None
        current_bb_middle = bb_middle.iloc[-1] if not bb_middle.empty else None
        current_bb_lower = bb_lower.iloc[-1] if not bb_lower.empty else None
        
        # Determine trend
        trend = self.determine_trend(current_rsi) if current_rsi is not None else "Hold"
        
        return TechnicalIndicators(
            symbol=symbol,
            rsi=current_rsi,
            macd_line=current_macd_line,
            macd_signal=current_macd_signal,
            macd_histogram=current_macd_histogram,
            bb_upper=current_bb_upper,
            bb_middle=current_bb_middle,
            bb_lower=current_bb_lower,
            trend=trend
        )
    
    def get_fibonacci_levels(self, df: pd.DataFrame, symbol: str) -> FibonacciLevels:
        """Get Fibonacci retracement levels for a symbol"""
        if df.empty:
            return FibonacciLevels(
                symbol=symbol,
                levels={},
                price_min=0.0,
                price_max=0.0
            )
        
        levels = self.calculate_fibonacci_retracements(df)
        price_min = df['low'].min()
        price_max = df['high'].max()
        
        return FibonacciLevels(
            symbol=symbol,
            levels=levels,
            price_min=price_min,
            price_max=price_max
        )
