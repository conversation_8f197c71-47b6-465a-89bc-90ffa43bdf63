# 🚀 Cryptocurrency Dashboard REST API - Setup Guide

This guide will help you set up and run the REST API for the cryptocurrency dashboard.

## 📋 Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Internet connection (for fetching crypto data)

## 🛠️ Installation Steps

### 1. Install API Dependencies

```bash
pip install -r api_requirements.txt
```

This will install:
- FastAPI (web framework)
- Uvicorn (ASGI server)
- CCXT (cryptocurrency exchange library)
- Pandas & NumPy (data processing)
- Pydantic (data validation)
- Pytest & Requests (testing)

### 2. Start the API Server

#### Option A: Using the startup script (Recommended)
```bash
python start_api.py
```

#### Option B: Direct uvicorn command
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. Verify Installation

Once the server is running, you should see:
```
🚀 Starting Cryptocurrency Dashboard API...
📊 Features: Real-time prices, Technical indicators, Market screening, Alerts
📚 Documentation will be available at: http://localhost:8000/docs
```

## 🔗 Access Points

After starting the server:

- **API Base URL**: http://localhost:8000
- **Interactive Documentation**: http://localhost:8000/docs
- **Alternative Documentation**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## 🧪 Testing the API

### Quick Health Check
```bash
curl http://localhost:8000/health
```

### Run Example Usage
```bash
python example_usage.py
```

### Run Test Suite
```bash
# Make sure API is running first
python test_api.py

# Or with pytest
pytest test_api.py -v
```

## 📊 Example API Calls

### Get Available Exchanges
```bash
curl http://localhost:8000/exchanges
```

### Get Bitcoin Price
```bash
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/price"
```

### Get Historical Data
```bash
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/ohlcv?timeframe=1d&limit=10"
```

### Get Technical Indicators
```bash
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/indicators"
```

### Create Price Alert
```bash
curl -X POST "http://localhost:8000/alerts" \
  -H "Content-Type: application/json" \
  -d '{"symbol": "BTC/USDT", "price_threshold": 50000, "condition": "above"}'
```

## 🔧 Configuration

### Supported Exchanges
- **Binance**: Most liquid exchange with extensive symbol support
- **OKX**: Alternative exchange with good API coverage

### Supported Timeframes
- Minutes: 1m, 5m, 15m, 30m
- Hours: 1h, 2h, 4h, 6h, 8h, 12h
- Days: 1d, 3d
- Weeks: 1w
- Months: 1M

### Rate Limiting
The API automatically handles exchange rate limits. If you encounter rate limiting:
1. Reduce request frequency
2. Use longer timeframes
3. Limit the number of symbols queried

## 🚨 Troubleshooting

### Common Issues

#### 1. "Connection Error" when starting
- **Cause**: Port 8000 might be in use
- **Solution**: Use a different port:
  ```bash
  uvicorn main:app --host 0.0.0.0 --port 8001 --reload
  ```

#### 2. "Exchange not found" errors
- **Cause**: Exchange name case sensitivity
- **Solution**: Use lowercase exchange names (binance, okx)

#### 3. "Symbol not found" errors
- **Cause**: Symbol format or availability
- **Solution**: 
  - Use correct format: BTC/USDT (not BTCUSDT)
  - Check available symbols first: `/exchanges/{exchange}/symbols`

#### 4. Rate limiting errors
- **Cause**: Too many requests to exchange
- **Solution**: 
  - Add delays between requests
  - Use caching for repeated queries
  - Reduce request frequency

#### 5. Import errors
- **Cause**: Missing dependencies
- **Solution**: 
  ```bash
  pip install -r api_requirements.txt
  ```

### Debug Mode

To run with debug logging:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
```

## 📁 Project Structure

```
├── main.py                 # FastAPI application entry point
├── crypto_service.py       # Core cryptocurrency functionality
├── technical_indicators.py # Technical analysis calculations
├── models.py              # Pydantic data models
├── exceptions.py          # Custom exception classes
├── start_api.py           # Convenient startup script
├── test_api.py            # Test suite
├── example_usage.py       # Usage examples
├── api_requirements.txt   # API dependencies
├── API_README.md         # API documentation
└── SETUP_GUIDE.md        # This setup guide
```

## 🔒 Security Notes

⚠️ **Important**: This API is designed for development and testing purposes.

For production use:
1. Add authentication (API keys, JWT tokens)
2. Implement rate limiting
3. Add input validation and sanitization
4. Use HTTPS
5. Configure CORS properly
6. Add logging and monitoring

## 🤝 Getting Help

1. **Check the logs**: Look at the console output for error messages
2. **API Documentation**: Visit http://localhost:8000/docs for interactive docs
3. **Test endpoints**: Use the example_usage.py script
4. **Check exchange status**: Some symbols might not be available on all exchanges

## 🎯 Next Steps

1. ✅ Start the API server
2. ✅ Test with example_usage.py
3. ✅ Explore the interactive documentation
4. ✅ Try different exchanges and symbols
5. ✅ Set up price alerts
6. ✅ Integrate with your applications

Happy trading! 📈🚀
