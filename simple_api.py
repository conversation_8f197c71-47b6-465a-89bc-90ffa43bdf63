#!/usr/bin/env python3
"""
Simple Cryptocurrency Dashboard API - Fallback version
Uses basic HTTP server if FastAPI has dependency issues
"""

import json
import ccxt
import pandas as pd
import numpy as np
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time
import uuid
from datetime import datetime, timedelta

class CryptoAPI:
    """Simple crypto API class"""

    def __init__(self):
        self.exchanges = {
            "binance": ccxt.binance(),
            "okx": ccxt.okx()
        }

        for exchange in self.exchanges.values():
            exchange.enableRateLimit = True

        # In-memory storage for alerts
        self.alerts = []

    def get_exchanges(self):
        """Get available exchanges"""
        return [{"name": name, "id": exchange.id} for name, exchange in self.exchanges.items()]
    
    def get_symbols(self, exchange_name, limit=10):
        """Get symbols from exchange"""
        if exchange_name not in self.exchanges:
            return {"error": f"Exchange {exchange_name} not found"}
        
        try:
            exchange = self.exchanges[exchange_name]
            markets = exchange.load_markets()
            symbols = [s for s, market in markets.items() if market.get('spot', False)]
            return symbols[:limit]
        except Exception as e:
            return {"error": str(e)}
    
    def get_price(self, exchange_name, symbol):
        """Get current price"""
        if exchange_name not in self.exchanges:
            return {"error": f"Exchange {exchange_name} not found"}
        
        try:
            exchange = self.exchanges[exchange_name]
            ticker = exchange.fetch_ticker(symbol)
            return {
                "symbol": symbol,
                "price": ticker['last'],
                "timestamp": datetime.now().isoformat(),
                "exchange": exchange_name
            }
        except Exception as e:
            return {"error": str(e)}
    
    def get_ohlcv(self, exchange_name, symbol, timeframe="1d", limit=100):
        """Get historical data"""
        if exchange_name not in self.exchanges:
            return {"error": f"Exchange {exchange_name} not found"}
        
        try:
            exchange = self.exchanges[exchange_name]
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            data = []
            for candle in ohlcv:
                data.append({
                    "timestamp": datetime.fromtimestamp(candle[0] / 1000).isoformat(),
                    "open": candle[1],
                    "high": candle[2],
                    "low": candle[3],
                    "close": candle[4],
                    "volume": candle[5]
                })
            
            return {
                "symbol": symbol,
                "exchange": exchange_name,
                "timeframe": timeframe,
                "data": data,
                "count": len(data)
            }
        except Exception as e:
            return {"error": str(e)}

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI"""
        if len(prices) < period:
            return None

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[:period])
        avg_loss = np.mean(losses[:period])

        if avg_loss == 0:
            return 100

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """Calculate MACD"""
        if len(prices) < slow:
            return None, None, None

        prices_series = pd.Series(prices)
        ema_fast = prices_series.ewm(span=fast).mean()
        ema_slow = prices_series.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line

        return macd_line.iloc[-1], signal_line.iloc[-1], histogram.iloc[-1]

    def calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """Calculate Bollinger Bands"""
        if len(prices) < period:
            return None, None, None

        prices_series = pd.Series(prices)
        rolling_mean = prices_series.rolling(window=period).mean()
        rolling_std = prices_series.rolling(window=period).std()

        upper_band = rolling_mean + (rolling_std * std_dev)
        lower_band = rolling_mean - (rolling_std * std_dev)

        return upper_band.iloc[-1], rolling_mean.iloc[-1], lower_band.iloc[-1]

    def get_technical_indicators(self, exchange_name, symbol, timeframe="1d", limit=100):
        """Get technical indicators for a symbol"""
        if exchange_name not in self.exchanges:
            return {"error": f"Exchange {exchange_name} not found"}

        try:
            # Get historical data first
            historical_data = self.get_ohlcv(exchange_name, symbol, timeframe, limit)

            if "error" in historical_data:
                return historical_data

            if not historical_data.get("data"):
                return {"error": "No historical data available"}

            # Extract close prices
            close_prices = [candle["close"] for candle in historical_data["data"]]

            if len(close_prices) < 14:
                return {"error": "Not enough data for technical indicators"}

            # Calculate indicators
            rsi = self.calculate_rsi(close_prices)
            macd_line, macd_signal, macd_histogram = self.calculate_macd(close_prices)
            bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(close_prices)

            # Determine trend based on RSI
            trend = "Hold"
            if rsi is not None:
                if rsi < 30:
                    trend = "Buy"
                elif rsi > 70:
                    trend = "Sell"

            return {
                "symbol": symbol,
                "exchange": exchange_name,
                "rsi": rsi,
                "macd_line": macd_line,
                "macd_signal": macd_signal,
                "macd_histogram": macd_histogram,
                "bb_upper": bb_upper,
                "bb_middle": bb_middle,
                "bb_lower": bb_lower,
                "trend": trend,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"error": str(e)}

    def get_fibonacci_levels(self, exchange_name, symbol, timeframe="1d", limit=100):
        """Get Fibonacci retracement levels"""
        if exchange_name not in self.exchanges:
            return {"error": f"Exchange {exchange_name} not found"}

        try:
            # Get historical data
            historical_data = self.get_ohlcv(exchange_name, symbol, timeframe, limit)

            if "error" in historical_data:
                return historical_data

            if not historical_data.get("data"):
                return {"error": "No historical data available"}

            # Extract high and low prices
            highs = [candle["high"] for candle in historical_data["data"]]
            lows = [candle["low"] for candle in historical_data["data"]]

            price_max = max(highs)
            price_min = min(lows)

            if price_max == price_min:
                return {"error": "No price range for Fibonacci calculation"}

            diff = price_max - price_min

            levels = {
                "0%": price_max,
                "23.6%": price_max - (0.236 * diff),
                "38.2%": price_max - (0.382 * diff),
                "50%": price_max - (0.500 * diff),
                "61.8%": price_max - (0.618 * diff),
                "78.6%": price_max - (0.786 * diff),
                "100%": price_min
            }

            return {
                "symbol": symbol,
                "exchange": exchange_name,
                "levels": levels,
                "price_min": price_min,
                "price_max": price_max,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"error": str(e)}

    def create_alert(self, symbol, price_threshold, condition):
        """Create a new price alert"""
        alert_id = str(uuid.uuid4())
        alert = {
            "id": alert_id,
            "symbol": symbol,
            "price_threshold": float(price_threshold),
            "condition": condition.lower(),
            "triggered": False,
            "created_at": datetime.now().isoformat()
        }
        self.alerts.append(alert)
        return alert

    def get_alerts(self):
        """Get all alerts"""
        return {
            "alerts": self.alerts,
            "count": len(self.alerts)
        }

    def delete_alert(self, alert_id):
        """Delete an alert by ID"""
        for i, alert in enumerate(self.alerts):
            if alert["id"] == alert_id:
                del self.alerts[i]
                return {"message": "Alert deleted successfully"}
        return {"error": "Alert not found"}

    def check_alerts(self, exchange_name):
        """Check alerts and return triggered ones"""
        triggered_alerts = []

        for alert in self.alerts:
            if alert["triggered"]:
                continue

            try:
                # Get current price
                price_data = self.get_price(exchange_name, alert["symbol"])

                if "error" in price_data:
                    continue

                current_price = price_data["price"]
                should_trigger = False

                if alert["condition"] == "above" and current_price > alert["price_threshold"]:
                    should_trigger = True
                elif alert["condition"] == "below" and current_price < alert["price_threshold"]:
                    should_trigger = True

                if should_trigger:
                    alert["triggered"] = True
                    alert["triggered_at"] = datetime.now().isoformat()
                    alert["triggered_price"] = current_price
                    triggered_alerts.append(alert)

            except Exception:
                continue

        return triggered_alerts

    def get_market_screener(self, exchange_name, limit=10):
        """Get market screening data"""
        if exchange_name not in self.exchanges:
            return {"error": f"Exchange {exchange_name} not found"}

        try:
            # Get symbols
            symbols = self.get_symbols(exchange_name, limit * 2)  # Get more to filter

            if "error" in symbols:
                return symbols

            screener_items = []
            successful_fetches = 0

            for symbol in symbols:
                if successful_fetches >= limit:
                    break

                try:
                    # Get current price
                    price_data = self.get_price(exchange_name, symbol)

                    if "error" in price_data:
                        continue

                    # Get 24h change data (simplified - using 2 day comparison)
                    historical_data = self.get_ohlcv(exchange_name, symbol, '1d', 2)

                    change_24h = 0.0
                    if "data" in historical_data and len(historical_data["data"]) >= 2:
                        current_price = historical_data["data"][-1]["close"]
                        previous_price = historical_data["data"][-2]["close"]
                        change_24h = ((current_price - previous_price) / previous_price) * 100

                    # Determine status
                    status = '🔥' if change_24h > 5 else '❄️' if change_24h < -5 else '➡️'

                    screener_items.append({
                        "symbol": symbol,
                        "price": price_data["price"],
                        "change_24h": change_24h,
                        "status": status
                    })

                    successful_fetches += 1

                except Exception:
                    continue

            return {
                "exchange": exchange_name,
                "items": screener_items,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"error": str(e)}

class APIHandler(BaseHTTPRequestHandler):
    """HTTP request handler"""

    # Class-level API instance to persist alerts across requests
    _api_instance = None

    def __init__(self, *args, **kwargs):
        if APIHandler._api_instance is None:
            APIHandler._api_instance = CryptoAPI()
        self.api = APIHandler._api_instance
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        query_params = parse_qs(parsed_url.query)
        
        # Extract query parameters
        def get_param(name, default=None):
            return query_params.get(name, [default])[0]
        
        try:
            # Route handling
            if path == "/":
                response = {
                    "message": "Cryptocurrency Dashboard API (Simple Version)",
                    "version": "1.0.0-simple",
                    "endpoints": [
                        "/exchanges",
                        "/exchanges/{exchange}/symbols",
                        "/exchanges/{exchange}/symbols/{symbol}/price",
                        "/exchanges/{exchange}/symbols/{symbol}/ohlcv",
                        "/exchanges/{exchange}/symbols/{symbol}/indicators",
                        "/exchanges/{exchange}/symbols/{symbol}/fibonacci",
                        "/exchanges/{exchange}/screener",
                        "/alerts (GET/POST/DELETE)",
                        "/exchanges/{exchange}/alerts/check"
                    ]
                }
            
            elif path == "/health":
                response = {"status": "healthy"}
            
            elif path == "/exchanges":
                response = self.api.get_exchanges()
            
            elif path.startswith("/exchanges/") and path.endswith("/symbols"):
                # Extract exchange name
                parts = path.split("/")
                if len(parts) >= 3:
                    exchange_name = parts[2]
                    limit = int(get_param("limit", 10))
                    response = self.api.get_symbols(exchange_name, limit)
                else:
                    response = {"error": "Invalid path"}
            
            elif "/symbols/" in path and path.endswith("/price"):
                # Extract exchange and symbol
                parts = path.split("/")
                if len(parts) >= 5:
                    exchange_name = parts[2]
                    symbol = "/".join(parts[4:-1])  # Handle symbols like BTC/USDT
                    response = self.api.get_price(exchange_name, symbol)
                else:
                    response = {"error": "Invalid path"}
            
            elif "/symbols/" in path and path.endswith("/ohlcv"):
                # Extract exchange and symbol
                parts = path.split("/")
                if len(parts) >= 5:
                    exchange_name = parts[2]
                    symbol = "/".join(parts[4:-1])  # Handle symbols like BTC/USDT
                    timeframe = get_param("timeframe", "1d")
                    limit = int(get_param("limit", 100))
                    response = self.api.get_ohlcv(exchange_name, symbol, timeframe, limit)
                else:
                    response = {"error": "Invalid path"}

            elif "/symbols/" in path and path.endswith("/indicators"):
                # Extract exchange and symbol for technical indicators
                parts = path.split("/")
                if len(parts) >= 5:
                    exchange_name = parts[2]
                    symbol = "/".join(parts[4:-1])  # Handle symbols like BTC/USDT
                    timeframe = get_param("timeframe", "1d")
                    limit = int(get_param("limit", 100))
                    response = self.api.get_technical_indicators(exchange_name, symbol, timeframe, limit)
                else:
                    response = {"error": "Invalid path"}

            elif "/symbols/" in path and path.endswith("/fibonacci"):
                # Extract exchange and symbol for Fibonacci levels
                parts = path.split("/")
                if len(parts) >= 5:
                    exchange_name = parts[2]
                    symbol = "/".join(parts[4:-1])  # Handle symbols like BTC/USDT
                    timeframe = get_param("timeframe", "1d")
                    limit = int(get_param("limit", 100))
                    response = self.api.get_fibonacci_levels(exchange_name, symbol, timeframe, limit)
                else:
                    response = {"error": "Invalid path"}

            elif path.startswith("/exchanges/") and path.endswith("/screener"):
                # Market screener
                parts = path.split("/")
                if len(parts) >= 3:
                    exchange_name = parts[2]
                    limit = int(get_param("limit", 10))
                    response = self.api.get_market_screener(exchange_name, limit)
                else:
                    response = {"error": "Invalid path"}

            elif path == "/alerts":
                # Get all alerts
                response = self.api.get_alerts()

            elif path.startswith("/exchanges/") and path.endswith("/alerts/check"):
                # Check alerts
                parts = path.split("/")
                if len(parts) >= 3:
                    exchange_name = parts[2]
                    response = self.api.check_alerts(exchange_name)
                else:
                    response = {"error": "Invalid path"}
            
            else:
                response = {"error": "Endpoint not found"}
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode())
                return
            
            # Send successful response
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response, indent=2).encode())
            
        except Exception as e:
            # Send error response
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            error_response = {"error": f"Internal server error: {str(e)}"}
            self.wfile.write(json.dumps(error_response).encode())

    def do_POST(self):
        """Handle POST requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        try:
            # Read request body
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)

            if content_length > 0:
                request_data = json.loads(post_data.decode('utf-8'))
            else:
                request_data = {}

            if path == "/alerts":
                # Create alert
                symbol = request_data.get("symbol")
                price_threshold = request_data.get("price_threshold")
                condition = request_data.get("condition")

                if not all([symbol, price_threshold, condition]):
                    response = {"error": "Missing required fields: symbol, price_threshold, condition"}
                    self.send_response(400)
                else:
                    response = self.api.create_alert(symbol, price_threshold, condition)
                    self.send_response(200)
            else:
                response = {"error": "Endpoint not found"}
                self.send_response(404)

            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response, indent=2).encode())

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            error_response = {"error": f"Internal server error: {str(e)}"}
            self.wfile.write(json.dumps(error_response).encode())

    def do_DELETE(self):
        """Handle DELETE requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        try:
            if path.startswith("/alerts/"):
                # Delete alert
                alert_id = path.split("/")[-1]
                response = self.api.delete_alert(alert_id)

                if "error" in response:
                    self.send_response(404)
                else:
                    self.send_response(200)
            else:
                response = {"error": "Endpoint not found"}
                self.send_response(404)

            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response, indent=2).encode())

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            error_response = {"error": f"Internal server error: {str(e)}"}
            self.wfile.write(json.dumps(error_response).encode())

    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Custom log message"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def main():
    """Start the simple API server"""
    print("🚀 Starting Simple Cryptocurrency Dashboard API...")
    print("📊 This is a fallback version using basic HTTP server")
    print("💡 API will be available at: http://localhost:8000")
    print("🔗 Example: http://localhost:8000/exchanges")
    print("-" * 60)
    
    try:
        server = HTTPServer(('0.0.0.0', 8000), APIHandler)
        print("✅ Server started successfully!")
        print("Press Ctrl+C to stop the server")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
