#!/usr/bin/env python3
"""
Simple Cryptocurrency Dashboard API - Fallback version
Uses basic HTTP server if FastAPI has dependency issues
"""

import json
import ccxt
import pandas as pd
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time
from datetime import datetime, timedelta

class CryptoAPI:
    """Simple crypto API class"""
    
    def __init__(self):
        self.exchanges = {
            "binance": ccxt.binance(),
            "okx": ccxt.okx()
        }
        
        for exchange in self.exchanges.values():
            exchange.enableRateLimit = True

    def get_exchanges(self):
        """Get available exchanges"""
        return [{"name": name, "id": exchange.id} for name, exchange in self.exchanges.items()]
    
    def get_symbols(self, exchange_name, limit=10):
        """Get symbols from exchange"""
        if exchange_name not in self.exchanges:
            return {"error": f"Exchange {exchange_name} not found"}
        
        try:
            exchange = self.exchanges[exchange_name]
            markets = exchange.load_markets()
            symbols = [s for s, market in markets.items() if market.get('spot', False)]
            return symbols[:limit]
        except Exception as e:
            return {"error": str(e)}
    
    def get_price(self, exchange_name, symbol):
        """Get current price"""
        if exchange_name not in self.exchanges:
            return {"error": f"Exchange {exchange_name} not found"}
        
        try:
            exchange = self.exchanges[exchange_name]
            ticker = exchange.fetch_ticker(symbol)
            return {
                "symbol": symbol,
                "price": ticker['last'],
                "timestamp": datetime.now().isoformat(),
                "exchange": exchange_name
            }
        except Exception as e:
            return {"error": str(e)}
    
    def get_ohlcv(self, exchange_name, symbol, timeframe="1d", limit=100):
        """Get historical data"""
        if exchange_name not in self.exchanges:
            return {"error": f"Exchange {exchange_name} not found"}
        
        try:
            exchange = self.exchanges[exchange_name]
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            data = []
            for candle in ohlcv:
                data.append({
                    "timestamp": datetime.fromtimestamp(candle[0] / 1000).isoformat(),
                    "open": candle[1],
                    "high": candle[2],
                    "low": candle[3],
                    "close": candle[4],
                    "volume": candle[5]
                })
            
            return {
                "symbol": symbol,
                "exchange": exchange_name,
                "timeframe": timeframe,
                "data": data,
                "count": len(data)
            }
        except Exception as e:
            return {"error": str(e)}

class APIHandler(BaseHTTPRequestHandler):
    """HTTP request handler"""
    
    def __init__(self, *args, **kwargs):
        self.api = CryptoAPI()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        query_params = parse_qs(parsed_url.query)
        
        # Extract query parameters
        def get_param(name, default=None):
            return query_params.get(name, [default])[0]
        
        try:
            # Route handling
            if path == "/":
                response = {
                    "message": "Cryptocurrency Dashboard API (Simple Version)",
                    "version": "1.0.0-simple",
                    "endpoints": [
                        "/exchanges",
                        "/exchanges/{exchange}/symbols",
                        "/exchanges/{exchange}/symbols/{symbol}/price",
                        "/exchanges/{exchange}/symbols/{symbol}/ohlcv"
                    ]
                }
            
            elif path == "/health":
                response = {"status": "healthy"}
            
            elif path == "/exchanges":
                response = self.api.get_exchanges()
            
            elif path.startswith("/exchanges/") and path.endswith("/symbols"):
                # Extract exchange name
                parts = path.split("/")
                if len(parts) >= 3:
                    exchange_name = parts[2]
                    limit = int(get_param("limit", 10))
                    response = self.api.get_symbols(exchange_name, limit)
                else:
                    response = {"error": "Invalid path"}
            
            elif "/symbols/" in path and path.endswith("/price"):
                # Extract exchange and symbol
                parts = path.split("/")
                if len(parts) >= 5:
                    exchange_name = parts[2]
                    symbol = "/".join(parts[4:-1])  # Handle symbols like BTC/USDT
                    response = self.api.get_price(exchange_name, symbol)
                else:
                    response = {"error": "Invalid path"}
            
            elif "/symbols/" in path and path.endswith("/ohlcv"):
                # Extract exchange and symbol
                parts = path.split("/")
                if len(parts) >= 5:
                    exchange_name = parts[2]
                    symbol = "/".join(parts[4:-1])  # Handle symbols like BTC/USDT
                    timeframe = get_param("timeframe", "1d")
                    limit = int(get_param("limit", 100))
                    response = self.api.get_ohlcv(exchange_name, symbol, timeframe, limit)
                else:
                    response = {"error": "Invalid path"}
            
            else:
                response = {"error": "Endpoint not found"}
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode())
                return
            
            # Send successful response
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response, indent=2).encode())
            
        except Exception as e:
            # Send error response
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            error_response = {"error": f"Internal server error: {str(e)}"}
            self.wfile.write(json.dumps(error_response).encode())
    
    def log_message(self, format, *args):
        """Custom log message"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def main():
    """Start the simple API server"""
    print("🚀 Starting Simple Cryptocurrency Dashboard API...")
    print("📊 This is a fallback version using basic HTTP server")
    print("💡 API will be available at: http://localhost:8000")
    print("🔗 Example: http://localhost:8000/exchanges")
    print("-" * 60)
    
    try:
        server = HTTPServer(('0.0.0.0', 8000), APIHandler)
        print("✅ Server started successfully!")
        print("Press Ctrl+C to stop the server")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
