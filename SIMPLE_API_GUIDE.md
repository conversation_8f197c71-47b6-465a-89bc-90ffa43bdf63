# 🚀 Simple Cryptocurrency Dashboard API - Working Guide

## ✅ **Current Status: WORKING!**

Your REST API is now running successfully using the simple version that avoids dependency conflicts.

## 🏃‍♂️ **Quick Start**

### 1. Start the API Server
```bash
python simple_api.py
```

You should see:
```
🚀 Starting Simple Cryptocurrency Dashboard API...
📊 This is a fallback version using basic HTTP server
💡 API will be available at: http://localhost:8000
✅ Server started successfully!
```

### 2. Test the API
```bash
python test_simple_api.py
```

## 🔗 **Available Endpoints**

### **Health Check**
```bash
curl http://localhost:8000/health
```
Response:
```json
{
  "status": "healthy"
}
```

### **Root Information**
```bash
curl http://localhost:8000/
```
Response:
```json
{
  "message": "Cryptocurrency Dashboard API (Simple Version)",
  "version": "1.0.0-simple",
  "endpoints": [...]
}
```

### **Get Available Exchanges**
```bash
curl http://localhost:8000/exchanges
```
Response:
```json
[
  {"name": "binance", "id": "binance"},
  {"name": "okx", "id": "okx"}
]
```

### **Get Trading Symbols**
```bash
curl "http://localhost:8000/exchanges/binance/symbols?limit=10"
```
Response:
```json
[
  "ETH/BTC",
  "LTC/BTC",
  "BNB/BTC",
  "NEO/BTC",
  "QTUM/ETH"
]
```

### **Get Current Price**
```bash
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/price"
```
Response:
```json
{
  "symbol": "BTC/USDT",
  "price": 118024.68,
  "timestamp": "2025-07-21T21:24:48.420294",
  "exchange": "binance"
}
```

### **Get Historical Data**
```bash
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/ohlcv?timeframe=1d&limit=5"
```
Response:
```json
{
  "symbol": "BTC/USDT",
  "exchange": "binance",
  "timeframe": "1d",
  "data": [
    {
      "timestamp": "2025-07-19T03:00:00",
      "open": 117924.84,
      "high": 118499.9,
      "low": 117277.34,
      "close": 117840.0,
      "volume": 6635.80306
    }
  ],
  "count": 1
}
```

## 📊 **Real Examples with Live Data**

### **Bitcoin Price Monitoring**
```bash
# Get current BTC price
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/price"

# Get BTC daily candles for last 7 days
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/ohlcv?timeframe=1d&limit=7"

# Get BTC hourly data
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/ohlcv?timeframe=1h&limit=24"
```

### **Ethereum Analysis**
```bash
# ETH price
curl "http://localhost:8000/exchanges/binance/symbols/ETH/USDT/price"

# ETH historical data
curl "http://localhost:8000/exchanges/binance/symbols/ETH/USDT/ohlcv?timeframe=1d&limit=30"
```

### **Alternative Exchange (OKX)**
```bash
# Get symbols from OKX
curl "http://localhost:8000/exchanges/okx/symbols?limit=5"

# BTC price from OKX
curl "http://localhost:8000/exchanges/okx/symbols/BTC/USDT/price"
```

## 🛠️ **Supported Parameters**

### **Timeframes**
- `1m`, `5m`, `15m`, `30m` (minutes)
- `1h`, `2h`, `4h`, `6h`, `8h`, `12h` (hours)
- `1d`, `3d` (days)
- `1w` (week)

### **Query Parameters**
- `limit`: Number of results (default: varies by endpoint)
- `timeframe`: Time interval for OHLCV data (default: 1d)

## 🔧 **Integration Examples**

### **Python Integration**
```python
import requests

# Get BTC price
response = requests.get("http://localhost:8000/exchanges/binance/symbols/BTC/USDT/price")
price_data = response.json()
print(f"BTC Price: ${price_data['price']:,.2f}")

# Get historical data
response = requests.get("http://localhost:8000/exchanges/binance/symbols/BTC/USDT/ohlcv?limit=10")
historical = response.json()
for candle in historical['data']:
    print(f"{candle['timestamp']}: ${candle['close']:,.2f}")
```

### **JavaScript/Node.js Integration**
```javascript
// Get BTC price
fetch('http://localhost:8000/exchanges/binance/symbols/BTC/USDT/price')
  .then(response => response.json())
  .then(data => console.log(`BTC Price: $${data.price.toLocaleString()}`));

// Get historical data
fetch('http://localhost:8000/exchanges/binance/symbols/BTC/USDT/ohlcv?limit=5')
  .then(response => response.json())
  .then(data => {
    data.data.forEach(candle => {
      console.log(`${candle.timestamp}: $${candle.close.toLocaleString()}`);
    });
  });
```

### **Bash/Shell Integration**
```bash
#!/bin/bash
# Simple price monitoring script

echo "=== Crypto Price Monitor ==="
BTC_PRICE=$(curl -s "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/price" | jq -r '.price')
ETH_PRICE=$(curl -s "http://localhost:8000/exchanges/binance/symbols/ETH/USDT/price" | jq -r '.price')

echo "BTC: \$$(printf '%.2f' $BTC_PRICE)"
echo "ETH: \$$(printf '%.2f' $ETH_PRICE)"
```

## 🚨 **Important Notes**

1. **No Authentication**: This API is open and doesn't require API keys
2. **Rate Limiting**: Respects exchange rate limits automatically
3. **Error Handling**: Returns JSON error responses for invalid requests
4. **CORS Enabled**: Can be accessed from web browsers
5. **Real-time Data**: Fetches live data from exchanges

## 🔄 **Differences from Full FastAPI Version**

The simple API provides core functionality but lacks some advanced features:

**✅ Available:**
- Real-time price data
- Historical OHLCV data
- Multiple exchanges (Binance, OKX)
- All timeframes
- JSON responses
- Error handling

**❌ Not Available (yet):**
- Technical indicators (RSI, MACD, etc.)
- Fibonacci retracements
- Market screening
- Price alerts
- Interactive documentation
- Advanced data models

## 🎯 **Next Steps**

1. **Use the working API** for your current needs
2. **Integrate with your applications** using the examples above
3. **Monitor crypto prices** in real-time
4. **Fetch historical data** for analysis

## 🔧 **Fixing FastAPI Version (Optional)**

If you want the full-featured FastAPI version later:

```bash
# Create clean virtual environment
python -m venv crypto_env
source crypto_env/bin/activate  # Linux/Mac
# crypto_env\Scripts\activate  # Windows

# Install compatible versions
pip install --upgrade pip
pip install "typing-extensions>=4.8.0"
pip install "pydantic>=2.0.0,<2.6.0"
pip install "fastapi>=0.100.0,<0.105.0"
pip install "uvicorn[standard]>=0.23.0"

# Then try: python start_api.py
```

## 🎉 **Success!**

Your REST API is now working and ready for use! The simple version provides all the core cryptocurrency data functionality you need.

**API Base URL**: http://localhost:8000
**Status**: ✅ WORKING
**Features**: Real-time prices, Historical data, Multiple exchanges
