#!/usr/bin/env python3
"""
Example usage of the Cryptocurrency Dashboard API

This script demonstrates how to use the API endpoints
to fetch crypto data, analyze trends, and manage alerts.
"""

import requests
import json
import time
from datetime import datetime

# API Configuration
API_BASE = "http://localhost:8000"

def print_section(title):
    """Print a section header"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def make_request(method, endpoint, data=None):
    """Make API request with error handling"""
    url = f"{API_BASE}{endpoint}"
    try:
        if method.upper() == "GET":
            response = requests.get(url)
        elif method.upper() == "POST":
            response = requests.post(url, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def main():
    """Main example function"""
    print("🚀 Cryptocurrency Dashboard API - Example Usage")
    print("📚 Make sure the API server is running: python start_api.py")
    
    # 1. Check API Health
    print_section("1. Health Check")
    health = make_request("GET", "/health")
    if health:
        print(f"✅ API Status: {health['status']}")
    
    # 2. Get Available Exchanges
    print_section("2. Available Exchanges")
    exchanges = make_request("GET", "/exchanges")
    if exchanges:
        print(f"📊 Found {len(exchanges)} exchanges:")
        for exchange in exchanges:
            print(f"  • {exchange['name']} ({exchange['id']})")
    
    # 3. Get Trading Symbols
    print_section("3. Trading Symbols")
    symbols = make_request("GET", "/exchanges/binance/symbols?limit=10")
    if symbols:
        print(f"💰 First 10 symbols on Binance:")
        for symbol in symbols[:5]:
            print(f"  • {symbol}")
        if len(symbols) > 5:
            print(f"  ... and {len(symbols) - 5} more")
    
    # 4. Get Current Price
    print_section("4. Current Price Data")
    # Try to get BTC/USDT price
    price_data = make_request("GET", "/exchanges/binance/symbols/BTC/USDT/price")
    if price_data:
        print(f"₿ BTC/USDT Price: ${price_data['price']:,.2f}")
        print(f"📅 Timestamp: {price_data['timestamp']}")
        if price_data.get('volume'):
            print(f"📊 24h Volume: {price_data['volume']:,.2f}")
    
    # 5. Get Historical Data
    print_section("5. Historical Data")
    historical = make_request("GET", "/exchanges/binance/symbols/BTC/USDT/ohlcv?timeframe=1d&limit=5")
    if historical and historical['data']:
        print(f"📈 Last 5 daily candles for {historical['symbol']}:")
        for candle in historical['data'][-5:]:
            date = candle['timestamp'][:10]  # Extract date part
            print(f"  {date}: O:{candle['open']:,.0f} H:{candle['high']:,.0f} L:{candle['low']:,.0f} C:{candle['close']:,.0f}")
    
    # 6. Technical Indicators
    print_section("6. Technical Indicators")
    indicators = make_request("GET", "/exchanges/binance/symbols/BTC/USDT/indicators?timeframe=1d&limit=50")
    if indicators:
        print(f"🔍 Technical Analysis for {indicators['symbol']}:")
        if indicators.get('rsi'):
            print(f"  📊 RSI: {indicators['rsi']:.2f}")
        if indicators.get('trend'):
            print(f"  📈 Trend Signal: {indicators['trend']}")
        if indicators.get('macd_line'):
            print(f"  📉 MACD Line: {indicators['macd_line']:.4f}")
    
    # 7. Fibonacci Levels
    print_section("7. Fibonacci Retracement Levels")
    fibonacci = make_request("GET", "/exchanges/binance/symbols/BTC/USDT/fibonacci?timeframe=1d&limit=50")
    if fibonacci and fibonacci.get('levels'):
        print(f"🌀 Fibonacci levels for {fibonacci['symbol']}:")
        print(f"  📊 Price Range: ${fibonacci['price_min']:,.0f} - ${fibonacci['price_max']:,.0f}")
        for level, price in fibonacci['levels'].items():
            print(f"  • {level}: ${price:,.0f}")
    
    # 8. Market Screener
    print_section("8. Market Screener")
    screener = make_request("GET", "/exchanges/binance/screener?limit=5")
    if screener and screener.get('items'):
        print(f"🔍 Market Screening Results ({screener['exchange']}):")
        for item in screener['items']:
            change_color = "🔥" if item['change_24h'] > 0 else "❄️" if item['change_24h'] < 0 else "➡️"
            print(f"  {change_color} {item['symbol']}: ${item['price']:,.2f} ({item['change_24h']:+.2f}%)")
    
    # 9. Alert Management
    print_section("9. Price Alert Management")
    
    # Create an alert
    alert_data = {
        "symbol": "BTC/USDT",
        "price_threshold": 100000.0,  # High threshold for demo
        "condition": "above"
    }
    
    print(f"🔔 Creating alert: {alert_data['symbol']} {alert_data['condition']} ${alert_data['price_threshold']:,.0f}")
    alert = make_request("POST", "/alerts", alert_data)
    
    if alert:
        print(f"✅ Alert created with ID: {alert['id']}")
        
        # Get all alerts
        alerts = make_request("GET", "/alerts")
        if alerts:
            print(f"📋 Total alerts: {alerts['count']}")
        
        # Check alerts
        triggered = make_request("GET", "/exchanges/binance/alerts/check")
        if triggered is not None:
            print(f"⚡ Triggered alerts: {len(triggered)}")
        
        # Clean up - delete the alert
        delete_result = make_request("DELETE", f"/alerts/{alert['id']}")
        if delete_result:
            print(f"🗑️ Alert deleted successfully")
    
    # 10. API Documentation
    print_section("10. API Documentation")
    print("📚 Interactive API documentation available at:")
    print(f"  • Swagger UI: {API_BASE}/docs")
    print(f"  • ReDoc: {API_BASE}/redoc")
    
    print(f"\n🎉 Example completed! The API is ready for use.")
    print(f"💡 Tip: Check the API_README.md for more detailed usage examples.")

if __name__ == "__main__":
    main()
