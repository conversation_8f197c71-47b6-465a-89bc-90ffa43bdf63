#!/usr/bin/env python3
"""
Startup script for the Cryptocurrency Dashboard API
"""

import uvicorn
import sys
import os

def main():
    """Start the API server"""
    print("🚀 Starting Cryptocurrency Dashboard API...")
    print("📊 Features: Real-time prices, Technical indicators, Market screening, Alerts")
    print("📚 Documentation will be available at: http://localhost:8000/docs")
    print("🔄 Alternative docs at: http://localhost:8000/redoc")
    print("💡 Health check at: http://localhost:8000/health")
    print("-" * 60)
    
    try:
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,  # Enable auto-reload for development
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 API server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
