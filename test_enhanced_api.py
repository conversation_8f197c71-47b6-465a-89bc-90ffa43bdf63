#!/usr/bin/env python3
"""
Test the Enhanced Cryptocurrency Dashboard API
Including Technical Indicators and Price Alerts
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def test_endpoint(method, endpoint, description, data=None):
    """Test an API endpoint"""
    print(f"\n🔍 Testing: {description}")
    print(f"📡 {method} {endpoint}")
    
    try:
        url = f"{API_BASE}{endpoint}"
        if method.upper() == "GET":
            response = requests.get(url)
        elif method.upper() == "POST":
            response = requests.post(url, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url)
        
        if response.status_code in [200, 201]:
            response_data = response.json()
            print(f"✅ Success: {response.status_code}")
            
            # Pretty print with truncation for large responses
            if isinstance(response_data, dict) and len(str(response_data)) > 500:
                # Show key fields for large responses
                if "data" in response_data:
                    print(f"📊 Response: {json.dumps({k: v for k, v in response_data.items() if k != 'data'}, indent=2)}")
                    print(f"📈 Data points: {len(response_data['data'])}")
                else:
                    print(f"📊 Response: {json.dumps(response_data, indent=2)}")
            else:
                print(f"📊 Response: {json.dumps(response_data, indent=2)}")
            return response_data
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Exception: {e}")
        return None

def main():
    """Run enhanced API tests"""
    print("🧪 Testing Enhanced Cryptocurrency Dashboard API")
    print("🚀 New Features: Technical Indicators & Price Alerts")
    print("=" * 70)
    
    # Test 1: Basic endpoints
    test_endpoint("GET", "/health", "Health Check")
    test_endpoint("GET", "/", "Root Endpoint")
    
    # Test 2: Technical Indicators
    print(f"\n{'='*70}")
    print("📈 TECHNICAL INDICATORS TESTING")
    print(f"{'='*70}")
    
    indicators = test_endpoint("GET", "/exchanges/binance/symbols/BTC/USDT/indicators?timeframe=1d&limit=50", "BTC Technical Indicators")
    
    if indicators:
        print(f"\n📊 Technical Analysis Summary for BTC/USDT:")
        print(f"  • RSI: {indicators.get('rsi', 'N/A'):.2f}" + (" (Oversold)" if indicators.get('rsi', 50) < 30 else " (Overbought)" if indicators.get('rsi', 50) > 70 else " (Neutral)"))
        print(f"  • Trend Signal: {indicators.get('trend', 'N/A')}")
        print(f"  • MACD Line: {indicators.get('macd_line', 'N/A'):.2f}")
        print(f"  • Bollinger Upper: ${indicators.get('bb_upper', 'N/A'):,.2f}")
        print(f"  • Bollinger Lower: ${indicators.get('bb_lower', 'N/A'):,.2f}")
    
    # Test 3: Fibonacci Levels
    fibonacci = test_endpoint("GET", "/exchanges/binance/symbols/BTC/USDT/fibonacci?timeframe=1d&limit=30", "BTC Fibonacci Levels")
    
    if fibonacci and fibonacci.get('levels'):
        print(f"\n🌀 Fibonacci Retracement Levels for BTC/USDT:")
        print(f"  • Price Range: ${fibonacci['price_min']:,.0f} - ${fibonacci['price_max']:,.0f}")
        for level, price in fibonacci['levels'].items():
            print(f"  • {level}: ${price:,.0f}")
    
    # Test 4: Market Screener
    print(f"\n{'='*70}")
    print("🔍 MARKET SCREENER TESTING")
    print(f"{'='*70}")
    
    screener = test_endpoint("GET", "/exchanges/binance/screener?limit=5", "Market Screener")
    
    if screener and screener.get('items'):
        print(f"\n📋 Market Screening Results:")
        for item in screener['items']:
            change_color = "🔥" if item['change_24h'] > 0 else "❄️" if item['change_24h'] < 0 else "➡️"
            print(f"  {change_color} {item['symbol']}: ${item['price']:,.6f} ({item['change_24h']:+.2f}%)")
    
    # Test 5: Price Alerts
    print(f"\n{'='*70}")
    print("🔔 PRICE ALERTS TESTING")
    print(f"{'='*70}")
    
    # Create a test alert
    alert_data = {
        "symbol": "BTC/USDT",
        "price_threshold": 125000.0,  # High threshold for demo
        "condition": "above"
    }
    
    print(f"🔔 Creating alert: {alert_data['symbol']} {alert_data['condition']} ${alert_data['price_threshold']:,.0f}")
    alert = test_endpoint("POST", "/alerts", "Create Price Alert", alert_data)
    
    if alert:
        alert_id = alert['id']
        print(f"✅ Alert created with ID: {alert_id}")
        
        # Get all alerts
        alerts = test_endpoint("GET", "/alerts", "Get All Alerts")
        
        if alerts:
            print(f"📋 Total alerts: {alerts['count']}")
        
        # Check for triggered alerts
        triggered = test_endpoint("GET", "/exchanges/binance/alerts/check", "Check Triggered Alerts")
        
        if triggered is not None:
            print(f"⚡ Triggered alerts: {len(triggered)}")
            if triggered:
                for t_alert in triggered:
                    print(f"  🚨 {t_alert['symbol']}: ${t_alert['triggered_price']:,.2f} {t_alert['condition']} ${t_alert['price_threshold']:,.2f}")
        
        # Test alert deletion
        delete_result = test_endpoint("DELETE", f"/alerts/{alert_id}", "Delete Alert")
        
        if delete_result:
            print(f"🗑️ Alert deleted successfully")
    
    # Test 6: Create a realistic alert that might trigger
    print(f"\n🔔 Creating realistic alert (BTC below current price)...")
    realistic_alert = {
        "symbol": "BTC/USDT",
        "price_threshold": 115000.0,  # Below current price
        "condition": "below"
    }
    
    realistic_alert_response = test_endpoint("POST", "/alerts", "Create Realistic Alert", realistic_alert)
    
    if realistic_alert_response:
        print(f"✅ Realistic alert created")
        
        # Check if it triggers
        triggered_realistic = test_endpoint("GET", "/exchanges/binance/alerts/check", "Check Realistic Alert")
        
        if triggered_realistic:
            print(f"🚨 Alert triggered! BTC is below $115,000")
        else:
            print(f"⏳ Alert not triggered - BTC is above $115,000")
    
    # Test 7: Alternative exchange (OKX)
    print(f"\n{'='*70}")
    print("🔄 ALTERNATIVE EXCHANGE TESTING (OKX)")
    print(f"{'='*70}")
    
    test_endpoint("GET", "/exchanges/okx/symbols?limit=3", "OKX Symbols")
    test_endpoint("GET", "/exchanges/okx/symbols/BTC/USDT/price", "BTC Price from OKX")
    
    # Summary
    print(f"\n{'='*70}")
    print("🎉 ENHANCED API TESTING COMPLETE!")
    print(f"{'='*70}")
    
    print("\n✅ Working Features:")
    print("  • ✅ Real-time price data")
    print("  • ✅ Historical OHLCV data")
    print("  • ✅ Technical indicators (RSI, MACD, Bollinger Bands)")
    print("  • ✅ Fibonacci retracement levels")
    print("  • ✅ Market screening")
    print("  • ✅ Price alerts (create, check, delete)")
    print("  • ✅ Multiple exchanges (Binance, OKX)")
    
    print("\n📚 Available Endpoints:")
    print("  • GET /exchanges/{exchange}/symbols/{symbol}/indicators")
    print("  • GET /exchanges/{exchange}/symbols/{symbol}/fibonacci")
    print("  • GET /exchanges/{exchange}/screener")
    print("  • POST /alerts")
    print("  • GET /alerts")
    print("  • DELETE /alerts/{id}")
    print("  • GET /exchanges/{exchange}/alerts/check")
    
    print(f"\n🎯 API Base URL: {API_BASE}")
    print("💡 The Enhanced API is fully functional!")

if __name__ == "__main__":
    main()
