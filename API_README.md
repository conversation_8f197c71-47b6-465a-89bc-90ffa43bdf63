# Cryptocurrency Dashboard REST API

A comprehensive REST API for cryptocurrency data analysis, technical indicators, and market monitoring. Built with FastAPI and supports multiple exchanges.

## 🚀 Quick Start

### Installation

1. Install API dependencies:
```bash
pip install -r api_requirements.txt
```

2. Start the API server:
```bash
python start_api.py
```

3. Access the API documentation:
- Interactive docs: http://localhost:8000/docs
- Alternative docs: http://localhost:8000/redoc
- Health check: http://localhost:8000/health

## 📊 Features

- **Real-time Price Data**: Get current prices from Binance and OKX
- **Historical Data**: OHLCV data with various timeframes (1m, 5m, 1h, 1d, etc.)
- **Technical Indicators**: RSI, MACD, Bollinger Bands, Fibonacci retracements
- **Market Screening**: Analyze multiple symbols for trends and opportunities
- **Price Alerts**: Set and manage price threshold alerts
- **No Authentication**: Open API, no keys required

## 🔗 API Endpoints

### Exchanges
- `GET /exchanges` - List available exchanges
- `GET /exchanges/{exchange}/info` - Get exchange information

### Market Data
- `GET /exchanges/{exchange}/symbols` - Get available trading symbols
- `GET /exchanges/{exchange}/symbols/{symbol}/price` - Get current price
- `GET /exchanges/{exchange}/symbols/{symbol}/ohlcv` - Get historical data

### Technical Analysis
- `GET /exchanges/{exchange}/symbols/{symbol}/indicators` - Get technical indicators
- `GET /exchanges/{exchange}/symbols/{symbol}/fibonacci` - Get Fibonacci levels

### Market Screening
- `GET /exchanges/{exchange}/screener` - Get market screening data

### Alerts
- `POST /alerts` - Create price alert
- `GET /alerts` - Get all alerts
- `DELETE /alerts/{alert_id}` - Delete alert
- `GET /exchanges/{exchange}/alerts/check` - Check triggered alerts

## 📝 Example Usage

### Get Current Bitcoin Price
```bash
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/price"
```

### Get Historical Data
```bash
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/ohlcv?timeframe=1d&limit=30"
```

### Get Technical Indicators
```bash
curl "http://localhost:8000/exchanges/binance/symbols/BTC/USDT/indicators?timeframe=1d"
```

### Create Price Alert
```bash
curl -X POST "http://localhost:8000/alerts" \
  -H "Content-Type: application/json" \
  -d '{"symbol": "BTC/USDT", "price_threshold": 50000, "condition": "above"}'
```

### Market Screening
```bash
curl "http://localhost:8000/exchanges/binance/screener?limit=10"
```

## 🛠️ Development

### Project Structure
```
├── main.py                 # FastAPI application
├── crypto_service.py       # Core crypto functionality
├── technical_indicators.py # Technical analysis calculations
├── models.py              # Pydantic data models
├── exceptions.py          # Custom exceptions
├── start_api.py           # Startup script
├── api_requirements.txt   # API dependencies
└── API_README.md         # This file
```

### Running in Development Mode
```bash
python start_api.py
```

The server will start with auto-reload enabled for development.

### Error Handling
The API includes comprehensive error handling:
- 404 for not found resources (exchanges, symbols, alerts)
- 400 for validation errors
- 429 for rate limiting
- 500 for server errors

## 📚 Response Models

All endpoints return structured JSON responses with proper typing. See the interactive documentation at `/docs` for detailed schemas.

## 🔧 Configuration

### Supported Exchanges
- Binance
- OKX

### Supported Timeframes
- 1m, 5m, 15m, 30m (minutes)
- 1h, 2h, 4h, 6h, 8h, 12h (hours)
- 1d, 3d (days)
- 1w (week)
- 1M (month)

### Rate Limiting
The API respects exchange rate limits automatically. If you encounter rate limiting errors, reduce request frequency.

## 🚨 Important Notes

1. **No Authentication**: This API is designed for development/testing. In production, consider adding authentication.
2. **Rate Limits**: Respect exchange rate limits to avoid being blocked.
3. **Data Accuracy**: Prices and data are fetched in real-time but may have slight delays.
4. **Error Handling**: Always check response status codes and handle errors appropriately.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.
