"""
Custom exceptions for the Cryptocurrency Dashboard API
"""

class CryptoAPIException(Exception):
    """Base exception for crypto API errors"""
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class ExchangeNotFoundError(CryptoAPIException):
    """Raised when an exchange is not found"""
    def __init__(self, exchange_name: str):
        message = f"Exchange '{exchange_name}' not found"
        super().__init__(message, 404)

class SymbolNotFoundError(CryptoAPIException):
    """Raised when a symbol is not found"""
    def __init__(self, symbol: str, exchange: str = None):
        if exchange:
            message = f"Symbol '{symbol}' not found on exchange '{exchange}'"
        else:
            message = f"Symbol '{symbol}' not found"
        super().__init__(message, 404)

class DataFetchError(CryptoAPIException):
    """Raised when data fetching fails"""
    def __init__(self, message: str):
        super().__init__(f"Data fetch error: {message}", 500)

class RateLimitError(CryptoAPIException):
    """Raised when rate limit is exceeded"""
    def __init__(self, exchange: str = None):
        if exchange:
            message = f"Rate limit exceeded for exchange '{exchange}'"
        else:
            message = "Rate limit exceeded"
        super().__init__(message, 429)

class ValidationError(CryptoAPIException):
    """Raised when input validation fails"""
    def __init__(self, message: str):
        super().__init__(f"Validation error: {message}", 400)

class AlertNotFoundError(CryptoAPIException):
    """Raised when an alert is not found"""
    def __init__(self, alert_id: str):
        message = f"Alert with ID '{alert_id}' not found"
        super().__init__(message, 404)
