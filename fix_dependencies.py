#!/usr/bin/env python3
"""
Fix dependency issues for the Cryptocurrency Dashboard API
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ Success: {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running: {command}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    """Fix dependency issues"""
    print("🔧 Fixing dependency issues for Cryptocurrency Dashboard API...")
    
    # Step 1: Upgrade pip
    print("\n1. Upgrading pip...")
    run_command("python -m pip install --upgrade pip")
    
    # Step 2: Upgrade typing-extensions first
    print("\n2. Upgrading typing-extensions...")
    run_command("pip install --upgrade typing-extensions>=4.8.0")
    
    # Step 3: Install/upgrade pydantic with specific version
    print("\n3. Installing compatible pydantic...")
    run_command("pip install 'pydantic>=2.0.0,<2.6.0'")
    
    # Step 4: Install FastAPI with compatible version
    print("\n4. Installing compatible FastAPI...")
    run_command("pip install 'fastapi>=0.100.0,<0.105.0'")
    
    # Step 5: Install uvicorn
    print("\n5. Installing uvicorn...")
    run_command("pip install 'uvicorn[standard]>=0.23.0,<0.25.0'")
    
    # Step 6: Install other dependencies
    print("\n6. Installing other dependencies...")
    dependencies = [
        "ccxt>=4.0.0",
        "pandas>=1.5.0", 
        "numpy>=1.24.0",
        "python-multipart>=0.0.6",
        "pytest>=7.0.0",
        "requests>=2.28.0"
    ]
    
    for dep in dependencies:
        run_command(f"pip install '{dep}'")
    
    # Step 7: Verify installation
    print("\n7. Verifying installation...")
    try:
        import fastapi
        import uvicorn
        import ccxt
        import pandas
        import numpy
        import pydantic
        print("✅ All core dependencies imported successfully!")
        
        # Check versions
        print(f"FastAPI version: {fastapi.__version__}")
        print(f"Pydantic version: {pydantic.__version__}")
        print(f"CCXT version: {ccxt.__version__}")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Some dependencies may still have issues.")
        return False
    
    print("\n🎉 Dependencies fixed! You can now run:")
    print("python start_api.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n⚠️  If issues persist, try:")
        print("1. Create a new virtual environment:")
        print("   python -m venv crypto_api_env")
        print("   source crypto_api_env/bin/activate  # On Linux/Mac")
        print("   crypto_api_env\\Scripts\\activate     # On Windows")
        print("2. Then run this script again")
        sys.exit(1)
