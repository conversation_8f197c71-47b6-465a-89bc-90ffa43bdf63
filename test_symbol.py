#!/usr/bin/env python3

import ccxt
import sys

def test_symbol_on_exchanges(symbol):
    """Test if a symbol is available on different exchanges"""
    
    exchanges = {
        "Binance": ccxt.binance(),
        "OKX": ccxt.okx(),
    }
    
    print(f"Testing symbol: {symbol}")
    print("=" * 50)
    
    for exchange_name, exchange_obj in exchanges.items():
        print(f"\n{exchange_name}:")
        print("-" * 20)
        
        try:
            # Enable rate limiting
            exchange_obj.enableRateLimit = True
            
            # Load markets
            markets = exchange_obj.load_markets()
            
            # Check if symbol exists in markets
            if symbol in markets:
                market = markets[symbol]
                print(f"✅ Symbol found in markets")
                print(f"   Active: {market.get('active', 'Unknown')}")
                print(f"   Spot: {market.get('spot', 'Unknown')}")
                print(f"   Type: {market.get('type', 'Unknown')}")
                
                # Try to fetch ticker
                try:
                    ticker = exchange_obj.fetch_ticker(symbol)
                    last_price = ticker.get('last')
                    if last_price is not None:
                        print(f"✅ Price available: ${last_price:,.6f}")
                    else:
                        print(f"❌ Price is None in ticker")
                        print(f"   Ticker keys: {list(ticker.keys())}")
                except Exception as ticker_error:
                    print(f"❌ Error fetching ticker: {ticker_error}")
                    
            else:
                print(f"❌ Symbol not found in markets")
                
                # Look for similar symbols
                similar = [s for s in markets.keys() if '1000CAT' in s or 'CAT' in s]
                if similar:
                    print(f"   Similar symbols found: {similar[:5]}")
                else:
                    print(f"   No similar symbols found")
                    
        except Exception as e:
            print(f"❌ Error with {exchange_name}: {e}")

def test_alternative_symbols():
    """Test alternative symbol formats"""
    
    test_symbols = [
        "1000CAT/USDC",
        "1000CAT/USDT", 
        "CAT/USDC",
        "CAT/USDT",
        "1000CATUSDC",
        "1000CATUSDT"
    ]
    
    print("\n" + "=" * 60)
    print("TESTING ALTERNATIVE SYMBOL FORMATS")
    print("=" * 60)
    
    for symbol in test_symbols:
        test_symbol_on_exchanges(symbol)

if __name__ == "__main__":
    # Test the specific symbol that's causing issues
    test_symbol_on_exchanges("1000CAT/USDC")
    
    # Test alternatives
    test_alternative_symbols()
