# Cryptocurrency Symbol Compatibility Guide

## Issue: "❌ 1000CAT/USDC: Price unavailable"

This error occurs when a symbol is not available on the selected exchange.

## Exchange-Specific Symbol Availability

### Binance
✅ **Available:**
- 1000CAT/USDC (Price: ~$0.009290)
- 1000CAT/USDT (Price: ~$0.009290)

❌ **Not Available:**
- CAT/USDC
- CAT/USDT

### OKX
✅ **Available:**
- CAT/USDT (Price: ~$0.000009)
- CATI/USDT
- CATI/USDC

❌ **Not Available:**
- 1000CAT/USDC
- 1000CAT/USDT

## Solutions

### 1. Quick Fix: Switch Exchange
- If you want **1000CAT/USDC**: Use **Binance**
- If you want **CAT/USDT**: Use **OKX**

### 2. Use Compatible Symbols
- **On Binance**: Use 1000CAT/USDC or 1000CAT/USDT
- **On OKX**: Use CAT/USDT or CATI/USDT

### 3. App Improvements (Already Applied)
The app now provides:
- ⚠️ Better error messages showing which exchange doesn't support the symbol
- 💡 Suggestions for alternative symbols on the current exchange
- 💡 Suggestions to switch to other exchanges
- 🔍 Upfront validation warnings in the sidebar

## How to Use the App

1. **Select Exchange** in sidebar (Binance or OKX)
2. **Choose Symbol Source**: 
   - "Validated Only" = Pre-tested symbols that work
   - "All Available" = All symbols (some may not work)
3. **Select Cryptocurrencies** from the dropdown
4. **Check for warnings** in the sidebar before starting
5. **Click "Start Monitoring"**

## Pro Tips

- Use **"Validated Only"** symbol source for best reliability
- Check sidebar warnings before monitoring
- Switch exchanges if your desired symbol isn't available
- The app will now show helpful alternatives when symbols fail

## Common Symbol Patterns

- **1000CAT** tokens are often on Binance
- **CAT** (without 1000 prefix) tokens are often on OKX
- **USDT pairs** are more widely available than USDC pairs
- Different exchanges may use different naming conventions
