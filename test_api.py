"""
Test suite for the Cryptocurrency Dashboard API
"""

import pytest
import requests
import time
from typing import Dict, Any

# API base URL
BASE_URL = "http://localhost:8000"

class TestAPI:
    """Test class for API endpoints"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup for tests"""
        # Check if API is running
        try:
            response = requests.get(f"{BASE_URL}/health")
            assert response.status_code == 200
        except requests.exceptions.ConnectionError:
            pytest.skip("API server is not running. Start with: python start_api.py")
    
    def test_root_endpoint(self):
        """Test root endpoint"""
        response = requests.get(f"{BASE_URL}/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = requests.get(f"{BASE_URL}/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_get_exchanges(self):
        """Test getting available exchanges"""
        response = requests.get(f"{BASE_URL}/exchanges")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Check exchange structure
        exchange = data[0]
        assert "name" in exchange
        assert "id" in exchange
        assert "has_ohlcv" in exchange
        assert "has_ticker" in exchange
    
    def test_get_exchange_info(self):
        """Test getting specific exchange info"""
        response = requests.get(f"{BASE_URL}/exchanges/binance/info")
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Binance"
        assert data["id"] == "binance"
    
    def test_get_exchange_info_not_found(self):
        """Test getting info for non-existent exchange"""
        response = requests.get(f"{BASE_URL}/exchanges/nonexistent/info")
        assert response.status_code == 404
    
    def test_get_symbols(self):
        """Test getting symbols from exchange"""
        response = requests.get(f"{BASE_URL}/exchanges/binance/symbols?limit=10")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) <= 10
        
        if data:
            # Check symbol format
            symbol = data[0]
            assert isinstance(symbol, str)
            assert "/" in symbol  # Should be in format like BTC/USDT
    
    def test_get_current_price(self):
        """Test getting current price"""
        # Use a common symbol that should exist
        response = requests.get(f"{BASE_URL}/exchanges/binance/symbols/BTC/USDT/price")
        
        if response.status_code == 200:
            data = response.json()
            assert "symbol" in data
            assert "price" in data
            assert "timestamp" in data
            assert "exchange" in data
            assert data["price"] > 0
        else:
            # If BTC/USDT doesn't exist, that's also valid
            assert response.status_code in [404, 500]
    
    def test_get_historical_data(self):
        """Test getting historical data"""
        response = requests.get(
            f"{BASE_URL}/exchanges/binance/symbols/BTC/USDT/ohlcv"
            "?timeframe=1d&limit=5"
        )
        
        if response.status_code == 200:
            data = response.json()
            assert "symbol" in data
            assert "exchange" in data
            assert "timeframe" in data
            assert "data" in data
            assert "count" in data
            
            if data["data"]:
                candle = data["data"][0]
                assert "timestamp" in candle
                assert "open" in candle
                assert "high" in candle
                assert "low" in candle
                assert "close" in candle
                assert "volume" in candle
        else:
            # Symbol might not exist
            assert response.status_code in [404, 500]
    
    def test_get_technical_indicators(self):
        """Test getting technical indicators"""
        response = requests.get(
            f"{BASE_URL}/exchanges/binance/symbols/BTC/USDT/indicators"
            "?timeframe=1d&limit=50"
        )
        
        if response.status_code == 200:
            data = response.json()
            assert "symbol" in data
            # Other fields might be None if not enough data
        else:
            assert response.status_code in [404, 500]
    
    def test_get_fibonacci_levels(self):
        """Test getting Fibonacci levels"""
        response = requests.get(
            f"{BASE_URL}/exchanges/binance/symbols/BTC/USDT/fibonacci"
            "?timeframe=1d&limit=50"
        )
        
        if response.status_code == 200:
            data = response.json()
            assert "symbol" in data
            assert "levels" in data
            assert "price_min" in data
            assert "price_max" in data
        else:
            assert response.status_code in [404, 500]
    
    def test_market_screener(self):
        """Test market screener"""
        response = requests.get(f"{BASE_URL}/exchanges/binance/screener?limit=5")
        
        if response.status_code == 200:
            data = response.json()
            assert "exchange" in data
            assert "items" in data
            assert "timestamp" in data
            assert isinstance(data["items"], list)
        else:
            assert response.status_code in [404, 500]
    
    def test_alert_management(self):
        """Test alert creation, retrieval, and deletion"""
        # Create alert
        alert_data = {
            "symbol": "BTC/USDT",
            "price_threshold": 50000.0,
            "condition": "above"
        }
        
        response = requests.post(f"{BASE_URL}/alerts", json=alert_data)
        assert response.status_code == 200
        
        created_alert = response.json()
        assert "id" in created_alert
        assert created_alert["symbol"] == alert_data["symbol"]
        assert created_alert["price_threshold"] == alert_data["price_threshold"]
        assert created_alert["condition"] == alert_data["condition"]
        
        alert_id = created_alert["id"]
        
        # Get all alerts
        response = requests.get(f"{BASE_URL}/alerts")
        assert response.status_code == 200
        
        alerts_data = response.json()
        assert "alerts" in alerts_data
        assert "count" in alerts_data
        assert alerts_data["count"] >= 1
        
        # Delete alert
        response = requests.delete(f"{BASE_URL}/alerts/{alert_id}")
        assert response.status_code == 200
        
        # Verify deletion
        response = requests.delete(f"{BASE_URL}/alerts/{alert_id}")
        assert response.status_code == 404

def run_manual_tests():
    """Run manual tests without pytest"""
    print("🧪 Running manual API tests...")
    
    # Test health check
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"✅ Health check: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return
    
    # Test exchanges
    try:
        response = requests.get(f"{BASE_URL}/exchanges")
        if response.status_code == 200:
            exchanges = response.json()
            print(f"✅ Exchanges: Found {len(exchanges)} exchanges")
        else:
            print(f"❌ Exchanges: {response.status_code}")
    except Exception as e:
        print(f"❌ Exchanges failed: {e}")
    
    # Test symbols
    try:
        response = requests.get(f"{BASE_URL}/exchanges/binance/symbols?limit=5")
        if response.status_code == 200:
            symbols = response.json()
            print(f"✅ Symbols: Found {len(symbols)} symbols")
        else:
            print(f"❌ Symbols: {response.status_code}")
    except Exception as e:
        print(f"❌ Symbols failed: {e}")
    
    print("🏁 Manual tests completed!")

if __name__ == "__main__":
    run_manual_tests()
