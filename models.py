"""
Pydantic models for the Cryptocurrency Dashboard API
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class ExchangeInfo(BaseModel):
    """Information about a cryptocurrency exchange"""
    name: str = Field(..., description="Exchange name")
    id: str = Field(..., description="Exchange ID")
    has_ohlcv: bool = Field(..., description="Whether exchange supports OHLCV data")
    has_ticker: bool = Field(..., description="Whether exchange supports ticker data")
    rate_limit: Optional[int] = Field(None, description="Rate limit in requests per second")

class PriceData(BaseModel):
    """Current price data for a symbol"""
    symbol: str = Field(..., description="Trading symbol")
    price: float = Field(..., description="Current price")
    timestamp: datetime = Field(..., description="Timestamp of the price")
    exchange: str = Field(..., description="Exchange name")
    bid: Optional[float] = Field(None, description="Bid price")
    ask: Optional[float] = Field(None, description="Ask price")
    volume: Optional[float] = Field(None, description="24h volume")

class OHLCVData(BaseModel):
    """OHLCV candlestick data"""
    timestamp: datetime = Field(..., description="Timestamp")
    open: float = Field(..., description="Open price")
    high: float = Field(..., description="High price")
    low: float = Field(..., description="Low price")
    close: float = Field(..., description="Close price")
    volume: float = Field(..., description="Volume")

class HistoricalData(BaseModel):
    """Historical OHLCV data response"""
    symbol: str = Field(..., description="Trading symbol")
    exchange: str = Field(..., description="Exchange name")
    timeframe: str = Field(..., description="Timeframe")
    data: List[OHLCVData] = Field(..., description="OHLCV data points")
    count: int = Field(..., description="Number of data points")

class TechnicalIndicators(BaseModel):
    """Technical indicators for a symbol"""
    symbol: str = Field(..., description="Trading symbol")
    rsi: Optional[float] = Field(None, description="RSI value")
    macd_line: Optional[float] = Field(None, description="MACD line")
    macd_signal: Optional[float] = Field(None, description="MACD signal line")
    macd_histogram: Optional[float] = Field(None, description="MACD histogram")
    bb_upper: Optional[float] = Field(None, description="Bollinger Bands upper")
    bb_middle: Optional[float] = Field(None, description="Bollinger Bands middle")
    bb_lower: Optional[float] = Field(None, description="Bollinger Bands lower")
    trend: Optional[str] = Field(None, description="Trend signal (Buy/Sell/Hold)")

class FibonacciLevels(BaseModel):
    """Fibonacci retracement levels"""
    symbol: str = Field(..., description="Trading symbol")
    levels: Dict[str, float] = Field(..., description="Fibonacci levels")
    price_min: float = Field(..., description="Minimum price in range")
    price_max: float = Field(..., description="Maximum price in range")

class ScreenerItem(BaseModel):
    """Market screener item"""
    symbol: str = Field(..., description="Trading symbol")
    price: float = Field(..., description="Current price")
    change_24h: float = Field(..., description="24h price change percentage")
    volume_24h: Optional[float] = Field(None, description="24h volume")
    status: str = Field(..., description="Status indicator")

class ScreenerData(BaseModel):
    """Market screener response"""
    exchange: str = Field(..., description="Exchange name")
    items: List[ScreenerItem] = Field(..., description="Screener items")
    timestamp: datetime = Field(..., description="Data timestamp")

class Alert(BaseModel):
    """Price alert"""
    id: str = Field(..., description="Alert ID")
    symbol: str = Field(..., description="Trading symbol")
    price_threshold: float = Field(..., description="Price threshold")
    condition: str = Field(..., description="Condition (above/below)")
    triggered: bool = Field(False, description="Whether alert has been triggered")
    created_at: datetime = Field(..., description="Alert creation time")

class AlertCreate(BaseModel):
    """Create alert request"""
    symbol: str = Field(..., description="Trading symbol")
    price_threshold: float = Field(..., description="Price threshold")
    condition: str = Field(..., description="Condition (above/below)")

class AlertResponse(BaseModel):
    """Alert response"""
    alerts: List[Alert] = Field(..., description="List of alerts")
    count: int = Field(..., description="Number of alerts")

class ErrorResponse(BaseModel):
    """Error response"""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Error details")
