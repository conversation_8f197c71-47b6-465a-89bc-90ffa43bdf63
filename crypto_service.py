"""
Core cryptocurrency service for data fetching and analysis
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import time
import uuid

from models import *
from technical_indicators import TechnicalAnalysis

class CryptoService:
    """Main service class for cryptocurrency operations"""
    
    def __init__(self):
        """Initialize the crypto service with available exchanges"""
        self.exchanges = self._initialize_exchanges()
        self.technical_analysis = TechnicalAnalysis()
        self.alerts = []  # In-memory storage for alerts
        
    def _initialize_exchanges(self) -> Dict[str, ccxt.Exchange]:
        """Initialize available exchanges with proper configuration"""
        exchanges = {
            "binance": ccxt.binance(),
            "okx": ccxt.okx(),
        }
        
        # Configure exchanges
        for name, exchange in exchanges.items():
            exchange.enableRateLimit = True
            if name == "okx":
                # OKX works best with minimal configuration
                pass
            else:
                exchange.options = {
                    'defaultType': 'spot',
                    'adjustForTimeDifference': True,
                }
        
        return exchanges
    
    def get_available_exchanges(self) -> List[ExchangeInfo]:
        """Get list of available exchanges"""
        exchange_list = []
        for name, exchange in self.exchanges.items():
            exchange_list.append(ExchangeInfo(
                name=name.title(),
                id=exchange.id,
                has_ohlcv=exchange.has.get('fetchOHLCV', False),
                has_ticker=exchange.has.get('fetchTicker', False),
                rate_limit=getattr(exchange, 'rateLimit', None)
            ))
        return exchange_list
    
    def get_exchange_info(self, exchange_name: str) -> ExchangeInfo:
        """Get information about a specific exchange"""
        exchange_name = exchange_name.lower()
        if exchange_name not in self.exchanges:
            raise ValueError(f"Exchange '{exchange_name}' not found")
        
        exchange = self.exchanges[exchange_name]
        return ExchangeInfo(
            name=exchange_name.title(),
            id=exchange.id,
            has_ohlcv=exchange.has.get('fetchOHLCV', False),
            has_ticker=exchange.has.get('fetchTicker', False),
            rate_limit=getattr(exchange, 'rateLimit', None)
        )
    
    def get_symbols(self, exchange_name: str, limit: Optional[int] = None) -> List[str]:
        """Get available trading symbols for an exchange"""
        exchange_name = exchange_name.lower()
        if exchange_name not in self.exchanges:
            raise ValueError(f"Exchange '{exchange_name}' not found")
        
        exchange = self.exchanges[exchange_name]
        
        try:
            markets = exchange.load_markets()
            
            # Filter for active spot markets
            symbols = [s for s, market in markets.items()
                      if market.get('spot', False) and market.get('active', False)]
            
            # If no symbols found, try just spot markets
            if not symbols:
                symbols = [s for s, market in markets.items() if market.get('spot', False)]
            
            # If still no symbols, try all markets
            if not symbols:
                symbols = list(markets.keys())
            
            # Sort symbols and apply limit
            symbols.sort()
            if limit:
                symbols = symbols[:limit]
            
            return symbols
            
        except Exception as e:
            raise Exception(f"Error fetching symbols: {str(e)}")
    
    def get_current_price(self, exchange_name: str, symbol: str) -> PriceData:
        """Get current price for a symbol"""
        exchange_name = exchange_name.lower()
        if exchange_name not in self.exchanges:
            raise ValueError(f"Exchange '{exchange_name}' not found")
        
        exchange = self.exchanges[exchange_name]
        
        try:
            ticker = exchange.fetch_ticker(symbol)
            
            return PriceData(
                symbol=symbol,
                price=ticker['last'] or ticker['close'],
                timestamp=datetime.fromtimestamp(ticker['timestamp'] / 1000) if ticker['timestamp'] else datetime.now(),
                exchange=exchange_name,
                bid=ticker.get('bid'),
                ask=ticker.get('ask'),
                volume=ticker.get('baseVolume')
            )
            
        except Exception as e:
            raise Exception(f"Error fetching price for {symbol}: {str(e)}")
    
    def get_historical_data(
        self, 
        exchange_name: str, 
        symbol: str, 
        timeframe: str = "1d",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 100
    ) -> HistoricalData:
        """Get historical OHLCV data for a symbol"""
        exchange_name = exchange_name.lower()
        if exchange_name not in self.exchanges:
            raise ValueError(f"Exchange '{exchange_name}' not found")
        
        exchange = self.exchanges[exchange_name]
        
        try:
            # Parse dates
            since_timestamp = None
            if start_date:
                since_timestamp = int(pd.to_datetime(start_date).timestamp() * 1000)
            
            # Fetch OHLCV data
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since_timestamp, limit=limit)
            
            if not ohlcv:
                return HistoricalData(
                    symbol=symbol,
                    exchange=exchange_name,
                    timeframe=timeframe,
                    data=[],
                    count=0
                )
            
            # Convert to our format
            data_points = []
            for candle in ohlcv:
                data_points.append(OHLCVData(
                    timestamp=datetime.fromtimestamp(candle[0] / 1000),
                    open=candle[1],
                    high=candle[2],
                    low=candle[3],
                    close=candle[4],
                    volume=candle[5]
                ))
            
            # Filter by end_date if provided
            if end_date:
                end_dt = pd.to_datetime(end_date)
                data_points = [dp for dp in data_points if dp.timestamp <= end_dt]
            
            return HistoricalData(
                symbol=symbol,
                exchange=exchange_name,
                timeframe=timeframe,
                data=data_points,
                count=len(data_points)
            )
            
        except Exception as e:
            raise Exception(f"Error fetching historical data: {str(e)}")

    def get_technical_indicators(self, exchange_name: str, symbol: str, timeframe: str = "1d", limit: int = 100) -> TechnicalIndicators:
        """Get technical indicators for a symbol"""
        # Get historical data first
        historical_data = self.get_historical_data(exchange_name, symbol, timeframe, limit=limit)

        if not historical_data.data:
            return TechnicalIndicators(symbol=symbol)

        # Convert to DataFrame for analysis
        df_data = []
        for point in historical_data.data:
            df_data.append({
                'timestamp': point.timestamp,
                'open': point.open,
                'high': point.high,
                'low': point.low,
                'close': point.close,
                'volume': point.volume
            })

        df = pd.DataFrame(df_data)
        return self.technical_analysis.get_technical_indicators(df, symbol)

    def get_fibonacci_levels(self, exchange_name: str, symbol: str, timeframe: str = "1d", limit: int = 100) -> FibonacciLevels:
        """Get Fibonacci retracement levels for a symbol"""
        # Get historical data first
        historical_data = self.get_historical_data(exchange_name, symbol, timeframe, limit=limit)

        if not historical_data.data:
            return FibonacciLevels(symbol=symbol, levels={}, price_min=0.0, price_max=0.0)

        # Convert to DataFrame for analysis
        df_data = []
        for point in historical_data.data:
            df_data.append({
                'timestamp': point.timestamp,
                'open': point.open,
                'high': point.high,
                'low': point.low,
                'close': point.close,
                'volume': point.volume
            })

        df = pd.DataFrame(df_data)
        return self.technical_analysis.get_fibonacci_levels(df, symbol)

    def get_market_screener(self, exchange_name: str, limit: int = 10) -> ScreenerData:
        """Get market screening data"""
        exchange_name = exchange_name.lower()
        if exchange_name not in self.exchanges:
            raise ValueError(f"Exchange '{exchange_name}' not found")

        # Get symbols
        symbols = self.get_symbols(exchange_name, limit=limit * 2)  # Get more to filter

        screener_items = []
        successful_fetches = 0

        for symbol in symbols:
            if successful_fetches >= limit:
                break

            try:
                # Get current price
                price_data = self.get_current_price(exchange_name, symbol)

                # Get 24h change data
                historical_data = self.get_historical_data(
                    exchange_name, symbol, '1d',
                    start_date=(datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d'),
                    limit=2
                )

                change_24h = 0.0
                if len(historical_data.data) >= 2:
                    current_price = historical_data.data[-1].close
                    previous_price = historical_data.data[-2].close
                    change_24h = ((current_price - previous_price) / previous_price) * 100

                # Determine status
                status = '🔥' if change_24h > 5 else '❄️' if change_24h < -5 else '➡️'

                screener_items.append(ScreenerItem(
                    symbol=symbol,
                    price=price_data.price,
                    change_24h=change_24h,
                    volume_24h=price_data.volume,
                    status=status
                ))

                successful_fetches += 1

            except Exception:
                # Skip problematic symbols
                continue

        return ScreenerData(
            exchange=exchange_name,
            items=screener_items,
            timestamp=datetime.now()
        )

    def create_alert(self, symbol: str, price_threshold: float, condition: str) -> Alert:
        """Create a new price alert"""
        alert_id = str(uuid.uuid4())
        alert = Alert(
            id=alert_id,
            symbol=symbol,
            price_threshold=price_threshold,
            condition=condition.lower(),
            triggered=False,
            created_at=datetime.now()
        )
        self.alerts.append(alert)
        return alert

    def get_alerts(self) -> List[Alert]:
        """Get all alerts"""
        return self.alerts

    def delete_alert(self, alert_id: str) -> bool:
        """Delete an alert by ID"""
        for i, alert in enumerate(self.alerts):
            if alert.id == alert_id:
                del self.alerts[i]
                return True
        return False

    def check_alerts(self, exchange_name: str) -> List[Alert]:
        """Check alerts and return triggered ones"""
        triggered_alerts = []

        for alert in self.alerts:
            if alert.triggered:
                continue

            try:
                price_data = self.get_current_price(exchange_name, alert.symbol)
                current_price = price_data.price

                should_trigger = False
                if alert.condition == "above" and current_price > alert.price_threshold:
                    should_trigger = True
                elif alert.condition == "below" and current_price < alert.price_threshold:
                    should_trigger = True

                if should_trigger:
                    alert.triggered = True
                    triggered_alerts.append(alert)

            except Exception:
                # Skip if can't fetch price
                continue

        return triggered_alerts
