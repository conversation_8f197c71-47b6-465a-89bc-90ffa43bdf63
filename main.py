"""
Cryptocurrency Dashboard REST API

A FastAPI-based REST API for cryptocurrency data, technical indicators,
and market analysis. No authentication required.

Features:
- Real-time price data from multiple exchanges (Binance, OKX)
- Historical OHLCV data with various timeframes
- Technical indicators (RSI, MACD, Bollinger Bands, Fibonacci)
- Market screening and trend analysis
- Price alerts management
- Automatic API documentation
"""

from fastapi import FastAPI, HTTPException, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from typing import List, Optional, Dict, Any
import uvicorn
import logging

# Import our modules
from crypto_service import CryptoService
from models import *
from exceptions import *

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Cryptocurrency Dashboard API",
    description="""
    ## Cryptocurrency Dashboard REST API

    A comprehensive REST API for cryptocurrency data analysis and monitoring.

    ### Features:
    - 📊 **Real-time price data** from multiple exchanges (Binance, OKX)
    - 📈 **Historical OHLCV data** with various timeframes
    - 🔍 **Technical indicators** (RSI, MACD, Bollinger Bands, Fibonacci)
    - 📋 **Market screening** and trend analysis
    - 🔔 **Price alerts** management
    - 📚 **Automatic documentation** with interactive examples

    ### Getting Started:
    1. Choose an exchange from `/exchanges`
    2. Get available symbols from `/exchanges/{exchange}/symbols`
    3. Fetch current prices or historical data
    4. Calculate technical indicators
    5. Set up price alerts

    ### No Authentication Required
    This API is open and does not require authentication.
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    contact={
        "name": "Crypto Dashboard API",
        "url": "https://github.com/your-repo/crypto-dashboard",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
)

# Add CORS middleware to allow frontend access
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize crypto service
crypto_service = CryptoService()

# Custom exception handlers
@app.exception_handler(CryptoAPIException)
async def crypto_api_exception_handler(request: Request, exc: CryptoAPIException):
    """Handle custom crypto API exceptions"""
    logger.error(f"CryptoAPIException: {exc.message}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.message, "type": exc.__class__.__name__}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": str(exc)}
    )

@app.get("/", tags=["Root"])
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Cryptocurrency Dashboard API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "crypto-dashboard-api"}

# Exchange endpoints
@app.get("/exchanges", response_model=List[ExchangeInfo], tags=["Exchanges"])
async def get_exchanges():
    """Get list of available exchanges"""
    return crypto_service.get_available_exchanges()

@app.get("/exchanges/{exchange_name}/info", response_model=ExchangeInfo, tags=["Exchanges"])
async def get_exchange_info(exchange_name: str):
    """Get information about a specific exchange"""
    try:
        return crypto_service.get_exchange_info(exchange_name)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

# Symbol and market data endpoints
@app.get("/exchanges/{exchange_name}/symbols", response_model=List[str], tags=["Market Data"])
async def get_symbols(exchange_name: str, limit: Optional[int] = Query(None, description="Limit number of symbols returned")):
    """Get available trading symbols for an exchange"""
    try:
        return crypto_service.get_symbols(exchange_name, limit)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@app.get("/exchanges/{exchange_name}/symbols/{symbol}/price", response_model=PriceData, tags=["Market Data"])
async def get_current_price(exchange_name: str, symbol: str):
    """Get current price for a symbol"""
    try:
        return crypto_service.get_current_price(exchange_name, symbol)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching price: {str(e)}")

# Historical data endpoints
@app.get("/exchanges/{exchange_name}/symbols/{symbol}/ohlcv", response_model=HistoricalData, tags=["Historical Data"])
async def get_historical_data(
    exchange_name: str,
    symbol: str,
    timeframe: str = Query("1d", description="Timeframe (1m, 5m, 1h, 1d, etc.)"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    limit: Optional[int] = Query(100, description="Number of candles to return")
):
    """Get historical OHLCV data for a symbol"""
    try:
        return crypto_service.get_historical_data(exchange_name, symbol, timeframe, start_date, end_date, limit)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching historical data: {str(e)}")

# Technical indicators endpoints
@app.get("/exchanges/{exchange_name}/symbols/{symbol}/indicators", response_model=TechnicalIndicators, tags=["Technical Analysis"])
async def get_technical_indicators(
    exchange_name: str,
    symbol: str,
    timeframe: str = Query("1d", description="Timeframe (1m, 5m, 1h, 1d, etc.)"),
    limit: int = Query(100, description="Number of candles for calculation")
):
    """Get technical indicators for a symbol"""
    try:
        return crypto_service.get_technical_indicators(exchange_name, symbol, timeframe, limit)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating indicators: {str(e)}")

@app.get("/exchanges/{exchange_name}/symbols/{symbol}/fibonacci", response_model=FibonacciLevels, tags=["Technical Analysis"])
async def get_fibonacci_levels(
    exchange_name: str,
    symbol: str,
    timeframe: str = Query("1d", description="Timeframe (1m, 5m, 1h, 1d, etc.)"),
    limit: int = Query(100, description="Number of candles for calculation")
):
    """Get Fibonacci retracement levels for a symbol"""
    try:
        return crypto_service.get_fibonacci_levels(exchange_name, symbol, timeframe, limit)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating Fibonacci levels: {str(e)}")

# Market screening endpoints
@app.get("/exchanges/{exchange_name}/screener", response_model=ScreenerData, tags=["Market Screening"])
async def get_market_screener(
    exchange_name: str,
    limit: int = Query(10, description="Number of symbols to screen")
):
    """Get market screening data"""
    try:
        return crypto_service.get_market_screener(exchange_name, limit)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching screener data: {str(e)}")

# Alert management endpoints
@app.post("/alerts", response_model=Alert, tags=["Alerts"])
async def create_alert(alert_data: AlertCreate):
    """Create a new price alert"""
    try:
        return crypto_service.create_alert(
            alert_data.symbol,
            alert_data.price_threshold,
            alert_data.condition
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating alert: {str(e)}")

@app.get("/alerts", response_model=AlertResponse, tags=["Alerts"])
async def get_alerts():
    """Get all alerts"""
    alerts = crypto_service.get_alerts()
    return AlertResponse(alerts=alerts, count=len(alerts))

@app.delete("/alerts/{alert_id}", tags=["Alerts"])
async def delete_alert(alert_id: str):
    """Delete an alert by ID"""
    success = crypto_service.delete_alert(alert_id)
    if not success:
        raise HTTPException(status_code=404, detail="Alert not found")
    return {"message": "Alert deleted successfully"}

@app.get("/exchanges/{exchange_name}/alerts/check", response_model=List[Alert], tags=["Alerts"])
async def check_alerts(exchange_name: str):
    """Check alerts and return triggered ones"""
    try:
        return crypto_service.check_alerts(exchange_name)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error checking alerts: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
